{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "type": ["atom", "integer"], "source": ["atom", "id"], "primary_key": true, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "email"], "primary_key": false, "foreign_key": false, "nullable": false, "check_constraints": []}, "name": ["atom", "email"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "first_name"], "primary_key": false, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "last_name"], "primary_key": false, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "integer"], "source": ["atom", "age"], "primary_key": false, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "age"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": true, "type": ["atom", "integer"], "source": ["atom", "is_active"], "primary_key": false, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "is_active"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "profile_data"], "primary_key": false, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "profile_data"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": "[]", "type": ["atom", "string"], "source": ["atom", "tags"], "primary_key": false, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "tags"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "decimal"], "source": ["atom", "score"], "primary_key": false, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "score"], "type": ["atom", "decimal"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "birth_date"], "primary_key": false, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "birth_date"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "last_login_at"], "primary_key": false, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "last_login_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "inserted_at"], "primary_key": false, "foreign_key": false, "nullable": false, "check_constraints": []}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "updated_at"], "primary_key": false, "foreign_key": false, "nullable": false, "check_constraints": []}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "source": ["atom", "users"], "primary_key": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "type": ["atom", "integer"], "source": ["atom", "id"], "primary_key": true, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": {"attributes": {"indices": [{"attributes": {"name": ["atom", "users_is_active_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": true, "type": ["atom", "integer"], "source": ["atom", "is_active"], "primary_key": false, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "is_active"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_last_name_first_name_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "first_name"], "primary_key": false, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "last_name"], "primary_key": false, "foreign_key": false, "nullable": true, "check_constraints": []}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_email_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "email"], "primary_key": false, "foreign_key": false, "nullable": false, "check_constraints": []}, "name": ["atom", "email"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "unique": true}, "__struct__": "Index"}]}, "__struct__": "Indices"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}