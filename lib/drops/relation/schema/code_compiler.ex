defmodule Drops.Relation.Schema.CodeCompiler do
  @moduledoc """
  Compiler for converting Drops.Relation.Schema structures to Ecto schema AST.

  This module follows the same visitor pattern as Drops.SQL.Compiler and
  Drops.Relation.Schema.Compiler but works with Drops.Relation.Schema structs
  and converts them to quoted expressions for field definitions and attributes.

  The compiler replaces the SchemaFieldAST protocol approach with a more
  consistent compiler pattern that can recursively process schema components.

  ## Usage

      # Convert a Relation Schema to field AST
      schema = %Drops.Relation.Schema{...}
      field_asts = Drops.Relation.Schema.CodeCompiler.visit(schema, [])

  ## Examples

      iex> schema = %Drops.Relation.Schema{fields: [...], primary_key: ...}
      iex> asts = Drops.Relation.Schema.CodeCompiler.visit(schema, [])
      iex> is_list(asts)
      true
  """

  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{Field, PrimaryKey}

  @doc """
  Main entry point for converting Relation Schema to field AST.

  ## Parameters

  - `schema` - A Drops.Relation.Schema struct
  - `opts` - Optional compilation options

  ## Returns

  A list of quoted expressions containing field definitions and attributes.

  ## Examples

      iex> schema = %Drops.Relation.Schema{fields: [...], ...}
      iex> asts = Drops.Relation.Schema.CodeCompiler.visit(schema, [])
      iex> is_list(asts)
      true
  """
  def visit(%Schema{} = schema, opts) do
    # Process schema components in order
    primary_key_attrs = visit(schema.primary_key, Keyword.put(opts, :schema, schema))
    foreign_key_attrs = visit_foreign_key_attributes(schema.fields)
    field_definitions = visit(schema.fields, Keyword.put(opts, :schema, schema))

    # Combine all AST nodes, filtering out nils
    [primary_key_attrs, foreign_key_attrs, field_definitions]
    |> List.flatten()
    |> Enum.reject(&is_nil/1)
  end

  # Visit a PrimaryKey struct to generate @primary_key attribute AST
  def visit(%PrimaryKey{fields: fields}, opts) when is_list(fields) do
    case length(fields) do
      0 ->
        # No primary key
        nil

      1 ->
        # Single primary key - generate @primary_key attribute if needed
        field = List.first(fields)
        visit_primary_key_field(field, opts)

      _ ->
        # Composite primary key - generate @primary_key false to disable default
        quote do
          @primary_key false
        end
    end
  end

  def visit(nil, _opts), do: nil

  # Visit a list of Field structs to generate field definition AST
  def visit(fields, opts) when is_list(fields) do
    schema = opts[:schema]
    primary_key_fields = get_primary_key_field_names(schema.primary_key)

    Enum.map(fields, fn field ->
      # Determine field category for proper handling
      category = determine_field_category(field, primary_key_fields)
      visit_field_with_category(field, category, opts)
    end)
  end

  # Visit a Field struct to generate field definition AST
  def visit(%Field{} = field, opts) do
    schema = opts[:schema]
    primary_key_fields = get_primary_key_field_names(schema.primary_key)
    category = determine_field_category(field, primary_key_fields)
    visit_field_with_category(field, category, opts)
  end

  # Visit type tuples (parameterized types)
  def visit({type, opts}, _opts) when is_list(opts) do
    {type, opts}
  end

  def visit({type, opts}, _opts) when is_map(opts) do
    {type, Map.to_list(opts)}
  end

  # Visit atomic values (field names, types, etc.)
  def visit(value, _opts) when is_atom(value), do: value
  def visit(value, _opts) when is_binary(value), do: value
  def visit(value, _opts) when is_number(value), do: value

  # Visit enumerable structures recursively
  def visit(enumerable, opts) when is_map(enumerable) do
    # Process maps by visiting each key-value pair
    Enum.reduce(enumerable, %{}, fn {key, value}, acc ->
      visited_key = visit(key, opts)
      visited_value = visit(value, opts)
      Map.put(acc, visited_key, visited_value)
    end)
  end

  def visit(enumerable, opts) when is_list(enumerable) and not is_binary(enumerable) do
    # Process lists by visiting each element
    Enum.map(enumerable, &visit(&1, opts))
  end

  # Fallback for other values
  def visit(value, _opts), do: value

  # Visit individual field components
  defp visit_field_with_category(%Field{} = field, category, opts) do
    # Skip timestamp fields and single primary key fields
    cond do
      field.name in [:inserted_at, :updated_at] ->
        nil

      category == :single_primary_key ->
        # Single primary key handled by @primary_key attribute
        nil

      true ->
        # Generate field definition
        visit_field_definition(field, category, opts)
    end
  end

  # Generate field definition AST
  defp visit_field_definition(%Field{} = field, category, _opts) do
    field_name = visit(field.name, [])
    field_type = visit(field.type, [])
    field_opts = visit_field_options(field, category)

    if field_opts == [] do
      quote do
        Ecto.Schema.field(unquote(field_name), unquote(field_type))
      end
    else
      quote do
        Ecto.Schema.field(unquote(field_name), unquote(field_type), unquote(field_opts))
      end
    end
  end

  # Visit field options from metadata - break down into individual visitors
  defp visit_field_options(%Field{} = field, category) do
    # Visit each metadata component individually
    source_opts = visit_meta_component(:source, field.name, field.meta)
    default_opts = visit_meta_component(:default, field.name, field.meta)
    pk_opts = visit_category_options(category)
    type_opts = visit_type_component(field.type)

    # Merge all options (skip nullable as it's not a valid Ecto field option)
    [type_opts, source_opts, default_opts, pk_opts]
    |> Enum.reduce([], &Keyword.merge/2)
  end

  # Visit individual metadata components
  defp visit_meta_component(:source, field_name, meta) do
    source = visit(Map.get(meta, :source, field_name), [])

    if source != field_name do
      [source: source]
    else
      []
    end
  end

  defp visit_meta_component(:default, _field_name, meta) do
    case Map.get(meta, :default) do
      nil -> []
      value -> [default: visit(value, [])]
    end
  end

  defp visit_meta_component(_key, _field_name, _meta), do: []

  # Visit category-specific options
  defp visit_category_options(:composite_primary_key), do: [primary_key: true]
  defp visit_category_options(_), do: []

  # Visit type component for parameterized types
  defp visit_type_component(type) do
    case type do
      {_type, opts} when is_list(opts) -> opts
      {_type, opts} when is_map(opts) -> Map.to_list(opts)
      _ -> []
    end
  end

  # Visit primary key field to generate @primary_key attribute
  defp visit_primary_key_field(%Field{} = field, _opts) do
    # Check if this field needs a custom @primary_key attribute
    is_foreign_key = Map.get(field.meta, :foreign_key, false)

    cond do
      is_foreign_key and field.type in [:binary_id, Ecto.UUID] ->
        quote do
          @foreign_key_type :binary_id
        end

      field.type == Ecto.UUID ->
        quote do
          @primary_key {unquote(field.name), Ecto.UUID, autogenerate: true}
        end

      field.type == :binary_id ->
        quote do
          @primary_key {unquote(field.name), :binary_id, autogenerate: true}
        end

      field.type not in [:id, :integer] ->
        quote do
          @primary_key {unquote(field.name), unquote(field.type), autogenerate: true}
        end

      true ->
        # Default :id type - no attribute needed
        nil
    end
  end

  # Visit foreign key attributes for fields that need @foreign_key_type
  defp visit_foreign_key_attributes(fields) do
    Enum.find_value(fields, fn field ->
      is_foreign_key = Map.get(field.meta, :foreign_key, false)

      if is_foreign_key and field.type in [:binary_id, Ecto.UUID] do
        quote do
          @foreign_key_type :binary_id
        end
      else
        nil
      end
    end)
  end

  # Helper functions

  defp get_primary_key_field_names(%PrimaryKey{fields: fields}) when is_list(fields) do
    Enum.map(fields, & &1.name)
  end

  defp get_primary_key_field_names(_), do: []

  defp determine_field_category(field, primary_key_fields) do
    cond do
      field.name in primary_key_fields and length(primary_key_fields) == 1 ->
        :single_primary_key

      field.name in primary_key_fields and length(primary_key_fields) > 1 ->
        :composite_primary_key

      true ->
        :regular
    end
  end
end
